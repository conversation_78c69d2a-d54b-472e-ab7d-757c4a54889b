<?php
date_default_timezone_set("Asia/Riyadh");
include_once('config.php');
include_once('init.php');
include_once('functions.php');
include_once('lang.php');
 
$actual_link = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
$Site_URL = GetTableSet('Site_URL');
$Logo = GetTableSet('Logo');
$Logo_Light = GetTableSet('Logo_Light');

$Site_Name = GetTableSet('Site_Name');
$Site_Title = GetTableSet('Site_Title');
$Description = GetTableSet('Description');
$Keywords = GetTableSet('Keywords');
$default_image = GetTableSet('default_image');
  
$Domain = parse_url($Site_URL)['host'];

$Site_URL = rtrim($Site_URL,"/");
$MasterColorForAll = GetTableSet('ThemeColor');
$MasterYear = date("Y");

 
$UmrahUrl = 'umrah-trips-';
$CheckoutUrl = 'https://web.whatsapp.com/';

$AllCities = getAllFrom('*' , 'cities' , 'WHERE status = 1 ', 'ORDER BY orders ASC , id ASC');
$AllHotelCities = getAllFrom('*' , 'hotel_city' , 'WHERE status = 1 ', 'ORDER BY orders ASC , id ASC');
$AllOffers = getAllFrom('*,offers.price as offer_price,offers.status as offer_status,offers.id as offer_id' , 'offers' , 'INNER JOIN hotels ON JSON_UNQUOTE(JSON_EXTRACT(offers.hotelid, "$.k")) = hotels.id WHERE offers.status = 1', 'ORDER BY offers.status DESC , offers.orders ASC , offers.id DESC');
$Loader = false;

<?php
include('header.php');
include('navbar.php');

// التحقق من وجود معرف الرحلة
if(!isset($_GET['trip_id']) || !isset($_GET['travel_date']) || !isset($_GET['seats_count'])) {
    header('Location: bus-booking.php');
    exit;
}

$tripId = $_GET['trip_id'];
$travelDate = $_GET['travel_date'];
$seatsCount = $_GET['seats_count'];

// جلب تفاصيل الرحلة
$trip = getAllFrom('*', 'bus_trips', 'WHERE id = '.$tripId.' AND status = 1', '')[0] ?? null;

if(!$trip) {
    header('Location: bus-booking.php');
    exit;
}

// حساب السعر الإجمالي
$totalPrice = $trip['seat_price'] * $seatsCount;

echo '
<section class="pt40 pb40">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">تأكيد حجز الرحلة</h4>
                    </div>
                    <div class="card-body">
                        <!-- تفاصيل الرحلة -->
                        <div class="trip-summary mb-4">
                            <h5>تفاصيل الرحلة</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>نوع الرحلة:</strong> '.($trip['trip_type'] == 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة').'</p>
                                    <p><strong>من:</strong> '.$trip['from_city'].' - '.$trip['from_station'].'</p>
                                    <p><strong>إلى:</strong> '.$trip['to_city'].' - '.$trip['to_station'].'</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>تاريخ السفر:</strong> '.date('Y-m-d', strtotime($travelDate)).'</p>
                                    <p><strong>وقت الانطلاق:</strong> '.date('g:i A', strtotime($trip['departure_time'])).'</p>
                                    <p><strong>وقت الوصول:</strong> '.date('g:i A', strtotime($trip['arrival_time'])).'</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>عدد المقاعد:</strong> '.$seatsCount.'</p>
                                    <p><strong>سعر المقعد:</strong> '.$trip['seat_price'].' ريال</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>المبلغ الإجمالي:</strong> <span class="text-primary font-weight-bold">'.$totalPrice.' ريال</span></p>
                                </div>
                            </div>
                        </div>

                        <!-- نموذج بيانات المسافر -->
                        <form id="busBookingForm">
                            <input type="hidden" name="trip_id" value="'.$tripId.'">
                            <input type="hidden" name="travel_date" value="'.$travelDate.'">
                            <input type="hidden" name="seats_count" value="'.$seatsCount.'">
                            <input type="hidden" name="total_amount" value="'.$totalPrice.'">
                            
                            <h5 class="mb-3">بيانات المسافر</h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="passenger_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="passenger_name" name="passenger_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="passenger_phone" class="form-label">رقم الجوال *</label>
                                    <input type="tel" class="form-control" id="passenger_phone" name="passenger_phone" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="passenger_email" class="form-label">البريد الإلكتروني (اختياري)</label>
                                    <input type="email" class="form-control" id="passenger_email" name="passenger_email">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="notes" class="form-label">ملاحظات إضافية (اختياري)</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                            </div>
                            
                            <!-- شروط وأحكام -->
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="terms_agreement" required>
                                <label class="form-check-label" for="terms_agreement">
                                    أوافق على <a href="terms-and-conditions" target="_blank">الشروط والأحكام</a>
                                </label>
                            </div>
                            
                            <!-- أزرار التحكم -->
                            <div class="d-flex justify-content-between">
                                <a href="bus-booking.php" class="btn btn-secondary">العودة للبحث</a>
                                <button type="submit" class="btn btn-primary">تأكيد الحجز</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.getElementById("busBookingForm").addEventListener("submit", function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append("action", "confirmBusBooking");
    
    // إظهار مؤشر التحميل
    const submitBtn = this.querySelector(\'button[type="submit"]\');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = \'<i class="fa fa-spinner fa-spin"></i> جاري التأكيد...\';
    submitBtn.disabled = true;
    
    fetch("ajax.php", {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert("تم تأكيد حجزك بنجاح! رقم الحجز: " + data.booking_reference);
            window.location.href = "bus-booking-confirmation.php?ref=" + data.booking_reference;
        } else {
            alert("حدث خطأ: " + data.error);
        }
    })
    .catch(error => {
        console.error("Error:", error);
        alert("حدث خطأ أثناء تأكيد الحجز");
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>

<style>
.trip-summary {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid var(--MasterColor);
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.card-header {
    background-color: var(--MasterColor);
    color: white;
}

.form-label {
    font-weight: 600;
    color: #333;
}

.form-control:focus {
    border-color: var(--MasterColor);
    box-shadow: 0 0 0 0.2rem rgba(var(--MasterColor), 0.25);
}

.btn-primary {
    background-color: var(--MasterColor);
    border-color: var(--MasterColor);
}

.btn-primary:hover {
    background-color: var(--MasterColor);
    border-color: var(--MasterColor);
    opacity: 0.9;
}

.text-primary {
    color: var(--MasterColor) !important;
}
</style>';

include('footer.php');
?>

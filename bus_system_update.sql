-- تحديث نظام حجز الباصات
-- تاريخ الإنشاء: 2025-07-29

-- إن<PERSON>اء جدول مدن الباصات
CREATE TABLE IF NOT EXISTS `bus_cities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` var<PERSON><PERSON>(255) NOT NULL COMMENT 'اسم المدينة',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة المدينة: 1=نشط، 0=غير نشط',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج مدن الباصات
INSERT INTO `bus_cities` (`name`, `status`) VALUES
('الرياض', 1),
('المدينة المنورة', 1),
('الدمام', 1),
('الاحساء', 1),
('جازان', 1),
('القصيم', 1),
('مكة المكرمة', 1);

-- التحقق من وجود جدول الرحلات وإنشاؤه إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `bus_trips` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trip_type` enum('to_mecca','from_mecca') NOT NULL COMMENT 'نوع الرحلة: ذهاب إلى مكة أو عودة من مكة',
  `from_city` varchar(255) NOT NULL COMMENT 'المدينة المنطلقة',
  `from_station` varchar(255) NOT NULL COMMENT 'محطة الانطلاق',
  `to_city` varchar(255) NOT NULL COMMENT 'المدينة الوجهة',
  `to_station` varchar(255) NOT NULL COMMENT 'محطة الوصول',
  `departure_time` time NOT NULL COMMENT 'وقت الانطلاق',
  `arrival_time` time NOT NULL COMMENT 'وقت الوصول',
  `seat_price` decimal(10,2) NOT NULL COMMENT 'سعر المقعد',
  `available_days` text NOT NULL COMMENT 'الأيام المتاحة: 0=يومياً، أو مصفوفة JSON مثل ["1","4"] للاثنين والخميس',
  `total_seats` int(11) NOT NULL DEFAULT 50 COMMENT 'إجمالي المقاعد',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة الرحلة: 1=نشط، 0=غير نشط',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_trip_type` (`trip_type`),
  KEY `idx_from_city` (`from_city`),
  KEY `idx_to_city` (`to_city`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- التحقق من وجود جدول الحجوزات وإنشاؤه إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `bus_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_reference` varchar(20) NOT NULL,
  `trip_id` int(11) NOT NULL,
  `passenger_name` varchar(255) NOT NULL,
  `passenger_phone` varchar(20) NOT NULL,
  `passenger_email` varchar(255) DEFAULT NULL,
  `travel_date` date NOT NULL,
  `seats_count` int(11) NOT NULL DEFAULT 1,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_status` enum('pending','paid','cancelled') NOT NULL DEFAULT 'pending',
  `booking_status` enum('confirmed','cancelled','completed') NOT NULL DEFAULT 'confirmed',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `booking_reference` (`booking_reference`),
  KEY `fk_trip_booking` (`trip_id`),
  KEY `idx_travel_date` (`travel_date`),
  KEY `idx_booking_status` (`booking_status`),
  CONSTRAINT `fk_trip_booking` FOREIGN KEY (`trip_id`) REFERENCES `bus_trips` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية للرحلات (إذا لم تكن موجودة)
INSERT IGNORE INTO `bus_trips` (`trip_type`, `from_city`, `from_station`, `to_city`, `to_station`, `departure_time`, `arrival_time`, `seat_price`, `available_days`, `total_seats`, `status`) VALUES
('to_mecca', 'الرياض', 'محطة البطحاء', 'مكة المكرمة', 'شارع ابراهيم الخليل', '13:30:00', '17:30:00', 100.00, '0', 50, 1),
('from_mecca', 'مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'الرياض', 'محطة البطحاء', '13:30:00', '17:30:00', 100.00, '0', 50, 1),
('to_mecca', 'المدينة المنورة', 'محطة السبعة مساجد', 'مكة المكرمة', 'شارع ابراهيم الخليل', '18:30:00', '22:30:00', 60.00, '0', 50, 1),
('from_mecca', 'مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'المدينة المنورة', 'محطة السبعة مساجد', '18:30:00', '22:30:00', 60.00, '0', 50, 1),
('to_mecca', 'الدمام', 'محطة الدمام المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '14:00:00', '20:00:00', 120.00, '["1","3","5"]', 50, 1),
('from_mecca', 'مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'الدمام', 'محطة الدمام المركزية', '15:00:00', '21:00:00', 120.00, '["2","4","6"]', 50, 1),
('to_mecca', 'الاحساء', 'محطة الاحساء', 'مكة المكرمة', 'شارع ابراهيم الخليل', '15:30:00', '21:30:00', 110.00, '["1","4"]', 50, 1),
('from_mecca', 'مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'الاحساء', 'محطة الاحساء', '16:00:00', '22:00:00', 110.00, '["2","5"]', 50, 1),
('to_mecca', 'جازان', 'محطة جازان المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '12:00:00', '18:00:00', 150.00, '0', 50, 1),
('from_mecca', 'مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'جازان', 'محطة جازان المركزية', '13:00:00', '19:00:00', 150.00, '0', 50, 1),
('to_mecca', 'القصيم', 'محطة القصيم المركزية', 'مكة المكرمة', 'شارع ابراهيم الخليل', '14:30:00', '19:30:00', 90.00, '["1","3","5","7"]', 50, 1),
('from_mecca', 'مكة المكرمة', 'محبس الجن أمام فندق كونكورد', 'القصيم', 'محطة القصيم المركزية', '15:30:00', '20:30:00', 90.00, '["1","3","5","7"]', 50, 1);

-- رسالة تأكيد
SELECT 'تم تحديث نظام حجز الباصات بنجاح!' as message;

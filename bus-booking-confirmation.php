<?php
include('header.php');
include('navbar.php');

// التحقق من وجود رقم الحجز
if(!isset($_GET['ref'])) {
    header('Location: bus-booking.php');
    exit;
}

$bookingRef = $_GET['ref'];

// جلب تفاصيل الحجز
$booking = getAllFrom('*', 'bus_bookings', 'WHERE booking_reference = "'.$bookingRef.'"', '')[0] ?? null;

if(!$booking) {
    header('Location: bus-booking.php');
    exit;
}

// جلب تفاصيل الرحلة
$trip = getAllFrom('*', 'bus_trips', 'WHERE id = '.$booking['trip_id'], '')[0] ?? null;

echo '
<section class="pt40 pb40">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header text-center">
                        <div class="success-icon mb-3">
                            <i class="fa fa-check-circle fa-3x text-success"></i>
                        </div>
                        <h4 class="mb-0">تم تأكيد حجزك بنجاح!</h4>
                        <p class="mb-0">رقم الحجز: <strong>'.$bookingRef.'</strong></p>
                    </div>
                    <div class="card-body">
                        <!-- تفاصيل الحجز -->
                        <div class="booking-details">
                            <h5 class="mb-3">تفاصيل الحجز</h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>اسم المسافر:</strong> '.$booking['passenger_name'].'</p>
                                    <p><strong>رقم الجوال:</strong> '.$booking['passenger_phone'].'</p>';
                                    if($booking['passenger_email']) {
                                        echo '<p><strong>البريد الإلكتروني:</strong> '.$booking['passenger_email'].'</p>';
                                    }
echo '                          </div>
                                <div class="col-md-6">
                                    <p><strong>تاريخ الحجز:</strong> '.date('Y-m-d H:i', strtotime($booking['created_at'])).'</p>
                                    <p><strong>حالة الحجز:</strong> <span class="badge badge-success">مؤكد</span></p>
                                    <p><strong>حالة الدفع:</strong> <span class="badge badge-warning">في انتظار الدفع</span></p>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h5 class="mb-3">تفاصيل الرحلة</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>نوع الرحلة:</strong> '.($trip['trip_type'] == 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة').'</p>
                                    <p><strong>من:</strong> '.$trip['from_city'].' - '.$trip['from_station'].'</p>
                                    <p><strong>إلى:</strong> '.$trip['to_city'].' - '.$trip['to_station'].'</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>تاريخ السفر:</strong> '.date('Y-m-d', strtotime($booking['travel_date'])).'</p>
                                    <p><strong>وقت الانطلاق:</strong> '.date('g:i A', strtotime($trip['departure_time'])).'</p>
                                    <p><strong>وقت الوصول:</strong> '.date('g:i A', strtotime($trip['arrival_time'])).'</p>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>عدد المقاعد:</strong> '.$booking['seats_count'].'</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>المبلغ الإجمالي:</strong> <span class="text-primary font-weight-bold">'.$booking['total_amount'].' ريال</span></p>
                                </div>
                            </div>';
                            
                            if($booking['notes']) {
                                echo '
                                <hr>
                                <h6>ملاحظات:</h6>
                                <p>'.$booking['notes'].'</p>';
                            }
                            
echo '                  </div>
                        
                        <!-- تعليمات مهمة -->
                        <div class="alert alert-info mt-4">
                            <h6><i class="fa fa-info-circle"></i> تعليمات مهمة:</h6>
                            <ul class="mb-0">
                                <li>يرجى الوصول إلى محطة الانطلاق قبل موعد الرحلة بـ 30 دقيقة على الأقل</li>
                                <li>تأكد من إحضار هويتك الشخصية</li>
                                <li>في حالة الإلغاء، يرجى التواصل معنا قبل 24 ساعة من موعد الرحلة</li>
                                <li>احتفظ برقم الحجز للمراجعة</li>
                            </ul>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="text-center mt-4">
                            <button onclick="window.print()" class="btn btn-secondary me-2">
                                <i class="fa fa-print"></i> طباعة التذكرة
                            </button>
                            <a href="bus-booking.php" class="btn btn-primary">
                                <i class="fa fa-plus"></i> حجز رحلة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.success-icon {
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.card-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.booking-details {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.badge-success {
    background-color: #28a745;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.text-primary {
    color: var(--MasterColor) !important;
}

.btn-primary {
    background-color: var(--MasterColor);
    border-color: var(--MasterColor);
}

.btn-primary:hover {
    background-color: var(--MasterColor);
    border-color: var(--MasterColor);
    opacity: 0.9;
}

@media print {
    .btn, .alert {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
</style>';

include('footer.php');
?>

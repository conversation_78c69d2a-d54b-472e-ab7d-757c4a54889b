<?php
include('webset.php');
include('session.php');
//-------------------------------------------------------------------------------
//-------------------------------------------------------------------------------
if(isset($_POST['do']) && $_POST['do'] == 'ConfirmBookTrip'){
    if($_POST['bt_name'] == ''){
        echo Show_Alert('danger' , 'من فضلك أدخل الأسم بالكامل.');
    }else if($_POST['bt_phone'] == ''){
        echo Show_Alert('danger' , 'من فضلك أدخل رقم الجوال.');
    }elseif(strlen($_POST['bt_phone']) < 8){
        echo Show_Alert('danger' , 'من فضلك أدخل رقم جوال صحيح.');
    }else{

            $stmt = $db->prepare("INSERT INTO orders ( name , phone , hamla_title , hamla_id , offer_title , offer_id , people , datee , madina , payment , room_title , return_date , total_price , discount ) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 ,:user_6 ,:user_7 ,:user_8 ,:user_9 ,:user_10 ,:user_11 ,:user_12 ,:user_13 ,:user_14 )");  
            $stmt->execute(array(
                'user_1' => $_POST['bt_name'] ,
                'user_2' => $_POST['bt_phone'] ,
                'user_3' => $_POST['hamla'] ,
                'user_4' => $_POST['id'] ,
                'user_5' => $_POST['title'] ,
                'user_6' => $_POST['bt_book'] ,
                'user_7' => $_POST['bt_people'] ,
                'user_8' => $_POST['date_timepicker_end'] ,
                'user_9' => $_POST['bt_mvist'] ,
                'user_10' => $_POST['selectedPayment'] ,
                'user_11' => $_POST['room_title'] ,
                'user_12' => $_POST['return_date_display'] ,
                'user_13' => $_POST['total_price'] ,
                'user_14' => $_POST['discount_display'] 
            ));
            echo 'done'; 

            $_SESSION['Omra'] = [
                'bt_name' => $_POST['bt_name'] ,
                'bt_phone' => $_POST['bt_phone'] ,
                'hamla' => $_POST['hamla'] , 
                'id' => $_POST['id'] , 
                'title' => $_POST['title'] , 
                'bt_book' => $_POST['bt_book'] , 
                'bt_people' => $_POST['bt_people'] , 
                'date_timepicker_end' => $_POST['date_timepicker_end'] , 
                'bt_mvist' => $_POST['bt_mvist'] , 
                'selectedPayment' => $_POST['selectedPayment'] , 
                'room_title' => $_POST['room_title'] , 
                
                'return_date_display' => $_POST['return_date_display'] , 
                'total_price' => $_POST['total_price'] , 
                'discount_display' => $_POST['discount_display'] , 
            ];


            $name  = $_POST['bt_name']  ?? '---';
            $phone =  $_POST['bt_phone'] ?? '---';
            $trip  = $_POST['title']  ?? '---';

            // الرسالة اللي هتتبعت
            $message = "تم تأكيد الحجز بنجاح:\n";
            $message .= "الاسم: $name\n";
            $message .= "الجوال: $phone\n";
            $message .= "الرحلة: ".$_POST['hamla'].PHP_EOL;
            $message .= "الغرفه: ".$_POST['room_title'].PHP_EOL;
            $message .= "تاريخ الرحله: ".$_POST['date_timepicker_end'].PHP_EOL;
            $message .= "التكلفه: ".$_POST['total_price'].PHP_EOL;



            $telegram_token = "**********************************************";
            $telegram_channel = "-4800733652";
        
            $data = [
                'chat_id' => $telegram_channel,
                'text' => $message 
            ];
            // 'teeeee' . PHP_EOL . PHP_EOL . 'test',
            try {
            $ch = curl_init("https://api.telegram.org/bot$telegram_token/sendMessage");
            curl_setopt($ch, CURLOPT_HEADER, false);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $result = curl_exec($ch);
            curl_close($ch);
            } catch (Exception $e) {} 

    }
//-------------------------------------------------------------------------------

}

//-------------------------------------------------------------------------------
// وظائف حجز الباص
//-------------------------------------------------------------------------------

// جلب المحطات بناءً على نوع الرحلة والمدينة
if(isset($_POST['action']) && $_POST['action'] == 'getStations'){
    $tripType = $_POST['trip_type'];
    $city = $_POST['city'];

    try {
        if($tripType == 'to_mecca') {
            // رحلات ذهاب إلى مكة - جلب محطات الانطلاق من المدينة المحددة
            $stmt = $db->prepare("SELECT DISTINCT from_station FROM bus_trips WHERE trip_type = 'to_mecca' AND from_city = ? AND status = 1");
            $stmt->execute([$city]);
        } else {
            // رحلات عودة من مكة - جلب محطات الانطلاق من مكة
            $stmt = $db->prepare("SELECT DISTINCT from_station FROM bus_trips WHERE trip_type = 'from_mecca' AND to_city = ? AND status = 1");
            $stmt->execute([$city]);
        }

        $stations = [];
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $stations[] = $row['from_station'];
        }

        echo json_encode(['success' => true, 'stations' => $stations]);
    } catch(Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// البحث عن الرحلات
if(isset($_POST['action']) && $_POST['action'] == 'searchTrips'){
    $tripType = $_POST['trip_type'];
    $city = $_POST['city'];
    $station = $_POST['station'];
    $travelDate = $_POST['travel_date'];
    $seatsCount = $_POST['seats_count'];

    try {
        // تحديد يوم الأسبوع للتاريخ المحدد (1=الاثنين، 7=الأحد)
        $dayOfWeek = date('N', strtotime($travelDate));

        if($tripType == 'to_mecca') {
            // رحلات ذهاب إلى مكة
            $stmt = $db->prepare("
                SELECT * FROM bus_trips
                WHERE trip_type = 'to_mecca'
                AND from_city = ?
                AND from_station = ?
                AND status = 1
                AND (available_days = '0' OR available_days LIKE ?)
            ");
            $stmt->execute([$city, $station, '%"'.$dayOfWeek.'"%']);
        } else {
            // رحلات عودة من مكة
            $stmt = $db->prepare("
                SELECT * FROM bus_trips
                WHERE trip_type = 'from_mecca'
                AND to_city = ?
                AND from_station = ?
                AND status = 1
                AND (available_days = '0' OR available_days LIKE ?)
            ");
            $stmt->execute([$city, $station, '%"'.$dayOfWeek.'"%']);
        }

        $trips = [];
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // تنسيق الوقت
            $row['departure_time'] = date('g:i A', strtotime($row['departure_time']));
            $row['arrival_time'] = date('g:i A', strtotime($row['arrival_time']));
            $trips[] = $row;
        }

        echo json_encode(['success' => true, 'trips' => $trips]);
    } catch(Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// تأكيد حجز الرحلة
if(isset($_POST['action']) && $_POST['action'] == 'confirmBusBooking'){
    $tripId = $_POST['trip_id'];
    $travelDate = $_POST['travel_date'];
    $seatsCount = $_POST['seats_count'];
    $totalAmount = $_POST['total_amount'];
    $passengerName = $_POST['passenger_name'];
    $passengerPhone = $_POST['passenger_phone'];
    $passengerEmail = $_POST['passenger_email'] ?? null;
    $notes = $_POST['notes'] ?? null;

    // التحقق من صحة البيانات
    if(empty($passengerName) || empty($passengerPhone)) {
        echo json_encode(['success' => false, 'error' => 'يرجى ملء جميع الحقول المطلوبة']);
        exit;
    }

    try {
        // التحقق من توفر الرحلة
        $stmt = $db->prepare("SELECT * FROM bus_trips WHERE id = ? AND status = 1");
        $stmt->execute([$tripId]);
        $trip = $stmt->fetch(PDO::FETCH_ASSOC);

        if(!$trip) {
            echo json_encode(['success' => false, 'error' => 'الرحلة غير متاحة']);
            exit;
        }

        // إنشاء رقم مرجعي للحجز
        $bookingReference = 'BUS' . date('Ymd') . rand(1000, 9999);

        // التأكد من عدم تكرار رقم الحجز
        $stmt = $db->prepare("SELECT id FROM bus_bookings WHERE booking_reference = ?");
        $stmt->execute([$bookingReference]);
        while($stmt->fetch()) {
            $bookingReference = 'BUS' . date('Ymd') . rand(1000, 9999);
            $stmt = $db->prepare("SELECT id FROM bus_bookings WHERE booking_reference = ?");
            $stmt->execute([$bookingReference]);
        }

        // إدراج الحجز في قاعدة البيانات
        $stmt = $db->prepare("
            INSERT INTO bus_bookings
            (booking_reference, trip_id, passenger_name, passenger_phone, passenger_email,
             travel_date, seats_count, total_amount, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $bookingReference,
            $tripId,
            $passengerName,
            $passengerPhone,
            $passengerEmail,
            $travelDate,
            $seatsCount,
            $totalAmount,
            $notes
        ]);

        echo json_encode(['success' => true, 'booking_reference' => $bookingReference]);

    } catch(Exception $e) {
        echo json_encode(['success' => false, 'error' => 'حدث خطأ أثناء تأكيد الحجز: ' . $e->getMessage()]);
    }
    exit;
}

//-------------------------------------------------------------------------------
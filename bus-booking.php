<?php
include('webset.php');
include('header.php');
include('navbar.php');

// جلب مدن الباصات من قاعدة البيانات
$busCities = getAllFrom('*', 'bus_cities', 'WHERE status=1', 'ORDER BY name ASC');

echo '
<section class="bus-booking-hero">
    <div class="container-fluid">
        <div class="row align-items-center min-vh-100">
            <!-- النص والعنوان مع صورة الباص -->
            <div class="col-lg-7 hero-text-section">
                <div class="hero-content">
                    <div class="hero-badge">
                        <span class="badge-text">مسافر على قدر</span>
                    </div>
                    <h1 class="hero-title">أسرع وأريح وسيلة سفر<br>في مصر</h1>
                    <div class="bus-image-container">
                        <img src="https://via.placeholder.com/600x300/ff8c00/ffffff?text=Bus+Image" alt="Bus" class="bus-image">
                    </div>
                </div>
            </div>
            
            <!-- نموذج الحجز -->
            <div class="col-lg-5 booking-section">
                <div class="booking-form-container">
                    <div class="booking-form-card">
                        <!-- عنوان النموذج -->
                        <div class="form-header mb-4">
                            <div class="trip-type-tabs">
                                <div class="tab-item active" data-type="to_mecca">
                                    <span class="tab-icon">⭕</span>
                                    <span>ذهاب فقط</span>
                                </div>
                                <div class="tab-item" data-type="from_mecca">
                                    <span class="tab-icon">✅</span>
                                    <span>ذهاب و عودة</span>
                                </div>
                            </div>
                        </div>
                        
                        <form id="busSearchForm">
                            <input type="hidden" id="tripType" name="trip_type" value="to_mecca">
                            
                            <!-- المدينة والمحطة -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">المغادرة من المحطة</label>
                                    <select class="form-select" id="citySelect">
                                        <option value="">اختر المدينة</option>';
                                        foreach($busCities as $city) {
                                            echo '<option value="'.$city['name'].'">'.$city['name'].'</option>';
                                        }
echo '                              </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">الوصول الى المحطة</label>
                                    <select class="form-select" id="stationSelect">
                                        <option value="">اختر المحطة</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- التاريخ وعدد المسافرين -->
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label class="form-label">التاريخ</label>
                                    <input type="date" id="travelDate" class="form-control" min="'.date('Y-m-d').'" value="'.date('Y-m-d').'" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">عدد المسافرين</label>
                                    <div class="passenger-counter">
                                        <button type="button" class="counter-btn minus" onclick="changePassengerCount(-1)">-</button>
                                        <span class="counter-value" id="passengerCount">1</span>
                                        <button type="button" class="counter-btn plus" onclick="changePassengerCount(1)">+</button>
                                        <input type="hidden" id="seatsCount" name="seats_count" value="1">
                                    </div>
                                    <small class="text-muted">يُنصح بحجز خدمة الانتقال من وإلى محطة الباص</small>
                                </div>
                            </div>
                            
                            <!-- زر البحث -->
                            <div class="text-center">
                                <button onclick="searchTrips()" class="btn-search" type="button">
                                    <i class="fa fa-search"></i>
                                    أعرض الرحلات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم النتائج -->
<section class="pt40 pb40" id="searchResults" style="display: none;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="search-results-header mb-4">
                    <h3>الرحلات المتاحة</h3>
                    <div id="resultsCount"></div>
                </div>
                <div id="tripsContainer">
                    <!-- سيتم عرض النتائج هنا -->
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.bus-booking-hero {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.bus-booking-hero::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 60%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1200 800\'%3E%3Cpath d=\'M0,400 Q300,200 600,400 T1200,400 L1200,800 L0,800 Z\' fill=\'rgba(255,255,255,0.1)\'/%3E%3C/svg%3E") no-repeat center;
    background-size: cover;
}

.hero-content {
    padding: 2rem;
    z-index: 2;
    position: relative;
}

.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 8px 20px;
    margin-bottom: 1rem;
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 14px;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    line-height: 1.2;
    margin-bottom: 2rem;
    text-align: right;
    padding-right: 2rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.booking-form-container {
    padding: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.booking-form-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 500px;
}

.trip-type-tabs {
    display: flex;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e0e0e0;
}

.tab-item {
    flex: 1;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.tab-item.active {
    background: #28a745;
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.tab-item i {
    display: block;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.tab-item span {
    display: block;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.tab-item small {
    color: #666;
    font-size: 0.8rem;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-select, .form-control {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #ff8c00;
    box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.25);
}

.passenger-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.5rem;
}

.counter-btn {
    background: #ff8c00;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.3s ease;
}

.counter-btn:hover {
    background: #ff6b00;
}

.counter-value {
    margin: 0 1rem;
    font-weight: bold;
    font-size: 1.1rem;
}

.btn-search {
    background: linear-gradient(135deg, #ff8c00, #ff6b00);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
}

.btn-search:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 140, 0, 0.4);
}

.btn-search i {
    margin-left: 0.5rem;
}

/* تنسيق نتائج البحث */
.trip-card .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.trip-card .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.trip-time, .arrival-time {
    text-align: center;
}

.trip-time .departure-time, .arrival-time .time {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

.route-info {
    color: #666;
    font-size: 0.9rem;
}

.trip-duration {
    color: #888;
}

.trip-duration i {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #ff8c00;
}

.trip-price .price {
    font-size: 1.3rem;
    font-weight: bold;
    color: #ff8c00;
}

.search-results-header {
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 1rem;
}

.hero-text-section {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

.hero-content {
    text-align: center;
    color: white;
    position: relative;
    z-index: 2;
}

.booking-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.bus-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

.bus-image {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
}

.tab-icon {
    margin-left: 8px;
    font-size: 16px;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
        text-align: center;
        padding-right: 0;
    }

    .booking-section {
        padding: 1rem;
    }

    .booking-form-card {
        padding: 1.5rem;
    }

    .hero-badge {
        position: static;
        margin-bottom: 1rem;
    }

    .trip-card .row > div {
        margin-bottom: 1rem;
    }

    .trip-time .departure-time, .arrival-time .time {
        font-size: 1.2rem;
    }
}
</style>

<script>
// تغيير نوع الرحلة
document.querySelectorAll(".tab-item").forEach(tab => {
    tab.addEventListener("click", function() {
        // إزالة الكلاس النشط من جميع التبويبات
        document.querySelectorAll(".tab-item").forEach(t => t.classList.remove("active"));

        // إضافة الكلاس النشط للتبويب المحدد
        this.classList.add("active");

        // تحديث نوع الرحلة
        const tripType = this.getAttribute("data-type");
        document.getElementById("tripType").value = tripType;

        // إعادة تعيين المدينة والمحطة
        document.getElementById("citySelect").value = "";
        document.getElementById("stationSelect").innerHTML = "<option value=\"\">اختر المحطة</option>";
    });
});

// تغيير عدد المسافرين
function changePassengerCount(change) {
    const countElement = document.getElementById("passengerCount");
    const hiddenInput = document.getElementById("seatsCount");
    let currentCount = parseInt(countElement.textContent);

    currentCount += change;

    if (currentCount < 1) currentCount = 1;
    if (currentCount > 10) currentCount = 10;

    countElement.textContent = currentCount;
    hiddenInput.value = currentCount;
}

// تحميل المحطات عند تغيير المدينة
document.getElementById("citySelect").addEventListener("change", function() {
    const city = this.value;
    const tripType = document.getElementById("tripType").value;
    const stationSelect = document.getElementById("stationSelect");

    if (city) {
        // إرسال طلب AJAX لجلب المحطات
        fetch("ajax.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: `action=getStations&city=${encodeURIComponent(city)}&trip_type=${tripType}`
        })
        .then(response => response.json())
        .then(data => {
            stationSelect.innerHTML = "<option value=\"\">اختر المحطة</option>";

            if (data.success && data.stations) {
                data.stations.forEach(station => {
                    stationSelect.innerHTML += `<option value="${station}">${station}</option>`;
                });
            }
        })
        .catch(error => {
            console.error("خطأ في تحميل المحطات:", error);
        });
    } else {
        stationSelect.innerHTML = "<option value=\"\">اختر المحطة</option>";
    }
});

// البحث عن الرحلات
function searchTrips() {
    const tripType = document.getElementById("tripType").value;
    const city = document.getElementById("citySelect").value;
    const station = document.getElementById("stationSelect").value;
    const travelDate = document.getElementById("travelDate").value;
    const seatsCount = document.getElementById("seatsCount").value;

    // التحقق من صحة البيانات
    if (!city) {
        alert("يرجى اختيار المدينة");
        return;
    }

    if (!station) {
        alert("يرجى اختيار المحطة");
        return;
    }

    if (!travelDate) {
        alert("يرجى اختيار تاريخ السفر");
        return;
    }

    // إرسال طلب البحث
    fetch("ajax.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `action=searchTrips&trip_type=${tripType}&city=${encodeURIComponent(city)}&station=${encodeURIComponent(station)}&travel_date=${travelDate}&seats_count=${seatsCount}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displaySearchResults(data.trips);
        } else {
            alert(data.error || "حدث خطأ أثناء البحث");
        }
    })
    .catch(error => {
        console.error("خطأ في البحث:", error);
        alert("حدث خطأ أثناء البحث");
    });
}

// عرض نتائج البحث
function displaySearchResults(trips) {
    const resultsSection = document.getElementById("searchResults");
    const tripsContainer = document.getElementById("tripsContainer");
    const resultsCount = document.getElementById("resultsCount");

    if (trips.length === 0) {
        tripsContainer.innerHTML =
            "<div class=\"alert alert-info text-center\">" +
                "<h5>لا توجد رحلات متاحة</h5>" +
                "<p>لم نجد رحلات متاحة للتاريخ والوجهة المحددة. يرجى تجربة تاريخ آخر.</p>" +
            "</div>";
        resultsCount.innerHTML = "لا توجد نتائج";
    } else {
        let tripsHtml = "";
        const travelDate = document.getElementById("travelDate").value;
        const seatsCount = document.getElementById("seatsCount").value;

        trips.forEach(function(trip) {
            const totalPrice = parseFloat(trip.seat_price) * parseInt(seatsCount);
            const tripCard = document.createElement("div");
            tripCard.className = "trip-card mb-3";
            tripCard.innerHTML =
                "<div class=\"card\">" +
                    "<div class=\"card-body\">" +
                        "<div class=\"row align-items-center\">" +
                            "<div class=\"col-md-3\">" +
                                "<div class=\"trip-time\">" +
                                    "<div class=\"departure-time\">" + trip.departure_time + "</div>" +
                                    "<div class=\"route-info\">" +
                                        "<small>" + trip.from_city + " - " + trip.from_station + "</small>" +
                                    "</div>" +
                                "</div>" +
                            "</div>" +
                            "<div class=\"col-md-3\">" +
                                "<div class=\"trip-duration text-center\">" +
                                    "<i class=\"fa fa-clock\"></i>" +
                                    "<div>مدة الرحلة</div>" +
                                    "<small>حوالي 4 ساعات</small>" +
                                "</div>" +
                            "</div>" +
                            "<div class=\"col-md-3\">" +
                                "<div class=\"arrival-time\">" +
                                    "<div class=\"time\">" + trip.arrival_time + "</div>" +
                                    "<div class=\"route-info\">" +
                                        "<small>" + trip.to_city + " - " + trip.to_station + "</small>" +
                                    "</div>" +
                                "</div>" +
                            "</div>" +
                            "<div class=\"col-md-3 text-center\">" +
                                "<div class=\"trip-price mb-2\">" +
                                    "<span class=\"price\">" + totalPrice + " ريال</span>" +
                                    "<small class=\"d-block\">للمسافر الواحد: " + trip.seat_price + " ريال</small>" +
                                "</div>" +
                                "<button class=\"btn btn-primary btn-sm book-btn\" data-trip-id=\"" + trip.id + "\" data-travel-date=\"" + travelDate + "\" data-seats=\"" + seatsCount + "\" data-total=\"" + totalPrice + "\">" +
                                    "احجز الآن" +
                                "</button>" +
                            "</div>" +
                        "</div>" +
                    "</div>" +
                "</div>";
            tripsHtml += tripCard.outerHTML;
        });

        tripsContainer.innerHTML = tripsHtml;

        // إضافة event listeners لأزرار الحجز
        document.querySelectorAll(".book-btn").forEach(function(btn) {
            btn.addEventListener("click", function() {
                const tripId = this.getAttribute("data-trip-id");
                const travelDate = this.getAttribute("data-travel-date");
                const seats = this.getAttribute("data-seats");
                const total = this.getAttribute("data-total");
                bookTrip(tripId, travelDate, seats, total);
            });
        });

        resultsCount.innerHTML = "تم العثور على " + trips.length + " رحلة";
    }

    resultsSection.style.display = "block";
    resultsSection.scrollIntoView({ behavior: "smooth" });
}

// حجز الرحلة
function bookTrip(tripId, travelDate, seatsCount, totalAmount) {
    const params = new URLSearchParams({
        trip_id: tripId,
        travel_date: travelDate,
        seats_count: seatsCount,
        total_amount: totalAmount
    });

    window.location.href = `bus-booking-form?${params.toString()}`;
}
</script>';

include('footer.php');
?>

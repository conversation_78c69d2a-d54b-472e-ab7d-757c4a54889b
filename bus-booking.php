<?php
include('header.php');
include('navbar.php');

// جلب المدن من قاعدة البيانات
$cities = getAllFrom('*', 'cities', 'WHERE status=1', 'ORDER BY name ASC');

echo '
<section class="home-banner-style1 p0">
    <div class="home-style1">
        <div class="container">
            <div class="row">
                <div class="col-xl-11 mx-auto">
                    <div class="inner-banner-style1 text-center">
                        <div class="advance-search-tab mt70 mt30-md mx-auto animate-up-3">
                            <div class="home-text-slider">
                                <div class="booking-text">
                                    <p><strong>باصات من والى مكة يومياً</strong></p> 
                                    <p>احجز رحلتك بأسعار تبدأ من <span class="price">60 ريال</span></p> 
                                </div>
                            </div>

                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                                    <div class="advance-content-style1">
                                        <form id="busSearchForm">
                                            <div class="row">
                                                <!-- نوع الرحلة -->
                                                <div class="col-md-12 mb20">
                                                    <p class="hero-text fz15 animate-up-3">نوع الرحلة</p>
                                                    <div class="d-flex">
                                                        <div class="selection dfc">
                                                            <input id="to_mecca" name="trip_type" type="radio" value="to_mecca" checked>
                                                            <label for="to_mecca">ذهاب الى مكة <img class="ic100" src="img/bus-vip.png"></label>
                                                        </div> 
                                                        <div class="selection dfc">
                                                            <input id="from_mecca" name="trip_type" type="radio" value="from_mecca">
                                                            <label for="from_mecca">عودة من مكة <img class="ic100" src="img/bus-st.png"></label>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- المدينة -->
                                                <div class="col-12 mb-3 p5">
                                                    <div class="ui-content mt-2">
                                                        <div class="form-style1">
                                                            <div class="bootselect-multiselect">
                                                                <select class="selectpicker" id="citySelect" data-live-search="true" data-width="100%">
                                                                    <option value="">اختر المدينة</option>';
                                                                    foreach($cities as $city) {
                                                                        echo '<option value="'.$city['name'].'">'.$city['name'].'</option>';
                                                                    }
echo '                                                          </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- المحطة -->
                                                <div class="col-12 mb-3 p5">
                                                    <div class="ui-content mt-2">
                                                        <div class="form-style1">
                                                            <div class="bootselect-multiselect">
                                                                <select class="selectpicker" id="stationSelect" data-live-search="true" data-width="100%">
                                                                    <option value="">اختر المحطة</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- تاريخ السفر -->
                                                <div class="col-md-6 mb-3 p5">
                                                    <div class="ui-content mt-2">
                                                        <div class="form-style1">
                                                            <input type="date" id="travelDate" class="form-control" min="'.date('Y-m-d').'" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- عدد المقاعد -->
                                                <div class="col-md-6 mb-3 p5">
                                                    <div class="ui-content mt-2">
                                                        <div class="form-style1">
                                                            <select class="selectpicker" id="seatsCount" data-width="100%">
                                                                <option value="1">1 مقعد</option>
                                                                <option value="2">2 مقعد</option>
                                                                <option value="3">3 مقاعد</option>
                                                                <option value="4">4 مقاعد</option>
                                                                <option value="5">5 مقاعد</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- زر البحث -->
                                                <div class="col-md-12">
                                                    <div class="d-flex align-items-center justify-content-start justify-content-lg-center mt-2">
                                                        <button onclick="searchTrips()" class="ud-btn btn-thm btn-block" type="button">
                                                            <span class="flaticon-search pe-2"></span> عرض الرحلات
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>              
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم عرض الرحلات -->
<section class="pt40 pb40" id="tripsSection" style="display: none;">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="main-title2 text-center">
                    <h2 class="title">الرحلات المتاحة</h2>
                </div>
            </div>
        </div>
        <div class="row" id="tripsResults">
            <!-- سيتم عرض الرحلات هنا -->
        </div>
    </div>
</section>

<script>
// تحديث المحطات بناءً على نوع الرحلة والمدينة
function updateStations() {
    const tripType = document.querySelector(\'input[name="trip_type"]:checked\').value;
    const selectedCity = document.getElementById(\'citySelect\').value;
    const stationSelect = document.getElementById(\'stationSelect\');
    
    // مسح المحطات الحالية
    stationSelect.innerHTML = \'<option value="">اختر المحطة</option>\';
    
    if (selectedCity) {
        // استدعاء AJAX لجلب المحطات
        fetch(\'ajax.php\', {
            method: \'POST\',
            headers: {
                \'Content-Type\': \'application/x-www-form-urlencoded\',
            },
            body: \'action=getStations&trip_type=\' + tripType + \'&city=\' + encodeURIComponent(selectedCity)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.stations.forEach(station => {
                    const option = document.createElement(\'option\');
                    option.value = station;
                    option.textContent = station;
                    stationSelect.appendChild(option);
                });
                $(\'#stationSelect\').selectpicker(\'refresh\');
            }
        })
        .catch(error => console.error(\'Error:\', error));
    }
    
    $(\'#stationSelect\').selectpicker(\'refresh\');
}

// البحث عن الرحلات
function searchTrips() {
    const tripType = document.querySelector(\'input[name="trip_type"]:checked\').value;
    const selectedCity = document.getElementById(\'citySelect\').value;
    const selectedStation = document.getElementById(\'stationSelect\').value;
    const travelDate = document.getElementById(\'travelDate\').value;
    const seatsCount = document.getElementById(\'seatsCount\').value;
    
    if (!selectedCity || !selectedStation || !travelDate) {
        alert(\'يرجى ملء جميع الحقول المطلوبة\');
        return;
    }
    
    // استدعاء AJAX للبحث عن الرحلات
    fetch(\'ajax.php\', {
        method: \'POST\',
        headers: {
            \'Content-Type\': \'application/x-www-form-urlencoded\',
        },
        body: \'action=searchTrips&trip_type=\' + tripType + 
              \'&city=\' + encodeURIComponent(selectedCity) + 
              \'&station=\' + encodeURIComponent(selectedStation) + 
              \'&travel_date=\' + travelDate + 
              \'&seats_count=\' + seatsCount
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayTrips(data.trips);
        } else {
            document.getElementById(\'tripsResults\').innerHTML = \'<div class="col-12 text-center"><p>لا توجد رحلات متاحة للمعايير المحددة</p></div>\';
        }
        document.getElementById(\'tripsSection\').style.display = \'block\';
    })
    .catch(error => {
        console.error(\'Error:\', error);
        alert(\'حدث خطأ أثناء البحث عن الرحلات\');
    });
}

// عرض الرحلات
function displayTrips(trips) {
    let html = \'\';
    trips.forEach(trip => {
        html += `
            <div class="col-lg-12 mb-3">
                <div class="card trip-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="trip-title">رحلة ${trip.trip_type === \'to_mecca\' ? \'ذهاب إلى\' : \'عودة من\'} ${trip.to_city}</h5>
                                <p class="trip-details">
                                    <i class="fa fa-clock-o"></i> انطلاق الساعة ${trip.departure_time} - 
                                    من ${trip.from_station} إلى ${trip.to_station} - 
                                    وصول الساعة ${trip.arrival_time}
                                </p>
                                <p class="trip-availability">
                                    ${trip.available_days === \'0\' ? \'متاح يومياً\' : \'متاح أيام محددة\'}
                                </p>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="trip-price">
                                    <span class="price">${trip.seat_price} ريال</span>
                                    <small>للمقعد الواحد</small>
                                </div>
                                <button onclick="bookTrip(${trip.id})" class="btn btn-primary mt-2">احجز الآن</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    document.getElementById(\'tripsResults\').innerHTML = html;
}

// حجز الرحلة
function bookTrip(tripId) {
    // يمكن إضافة نموذج حجز أو توجيه لصفحة الحجز
    window.location.href = \'bus-booking-form.php?trip_id=\' + tripId + 
                          \'&travel_date=\' + document.getElementById(\'travelDate\').value +
                          \'&seats_count=\' + document.getElementById(\'seatsCount\').value;
}

// إضافة مستمعي الأحداث
document.addEventListener(\'DOMContentLoaded\', function() {
    // تحديث المحطات عند تغيير نوع الرحلة
    document.querySelectorAll(\'input[name="trip_type"]\').forEach(radio => {
        radio.addEventListener(\'change\', updateStations);
    });
    
    // تحديث المحطات عند تغيير المدينة
    document.getElementById(\'citySelect\').addEventListener(\'change\', updateStations);
});
</script>

<style>
.trip-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    transition: box-shadow 0.3s ease;
}

.trip-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.trip-title {
    color: var(--MasterColor);
    font-weight: bold;
}

.trip-details {
    color: #666;
    margin-bottom: 5px;
}

.trip-availability {
    color: #28a745;
    font-size: 14px;
}

.trip-price .price {
    font-size: 24px;
    font-weight: bold;
    color: var(--MasterColor);
}

.trip-price small {
    display: block;
    color: #666;
}

.selection {
    margin-left: 20px;
}

.selection label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.selection input[type="radio"]:checked + label {
    border-color: var(--MasterColor);
    background-color: rgba(var(--MasterColor), 0.1);
}

.ic100 {
    width: 30px;
    height: 30px;
}
</style>';

include('footer.php');
?>
